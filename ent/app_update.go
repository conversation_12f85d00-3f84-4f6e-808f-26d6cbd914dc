// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"git.yingrtech.com/yingrtech/clerk/ent/app"
	"git.yingrtech.com/yingrtech/clerk/ent/predicate"
)

// AppUpdate is the builder for updating App entities.
type AppUpdate struct {
	config
	hooks    []Hook
	mutation *AppMutation
}

// Where appends a list predicates to the AppUpdate builder.
func (_u *AppUpdate) Where(ps ...predicate.App) *AppUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetName sets the "name" field.
func (_u *AppUpdate) SetName(v string) *AppUpdate {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *AppUpdate) SetNillableName(v *string) *AppUpdate {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetDescription sets the "description" field.
func (_u *AppUpdate) SetDescription(v string) *AppUpdate {
	_u.mutation.SetDescription(v)
	return _u
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (_u *AppUpdate) SetNillableDescription(v *string) *AppUpdate {
	if v != nil {
		_u.SetDescription(*v)
	}
	return _u
}

// SetAPIKey sets the "api_key" field.
func (_u *AppUpdate) SetAPIKey(v string) *AppUpdate {
	_u.mutation.SetAPIKey(v)
	return _u
}

// SetNillableAPIKey sets the "api_key" field if the given value is not nil.
func (_u *AppUpdate) SetNillableAPIKey(v *string) *AppUpdate {
	if v != nil {
		_u.SetAPIKey(*v)
	}
	return _u
}

// SetNotifyURL sets the "notify_url" field.
func (_u *AppUpdate) SetNotifyURL(v string) *AppUpdate {
	_u.mutation.SetNotifyURL(v)
	return _u
}

// SetNillableNotifyURL sets the "notify_url" field if the given value is not nil.
func (_u *AppUpdate) SetNillableNotifyURL(v *string) *AppUpdate {
	if v != nil {
		_u.SetNotifyURL(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *AppUpdate) SetStatus(v app.Status) *AppUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *AppUpdate) SetNillableStatus(v *app.Status) *AppUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetCreatedAt sets the "created_at" field.
func (_u *AppUpdate) SetCreatedAt(v time.Time) *AppUpdate {
	_u.mutation.SetCreatedAt(v)
	return _u
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (_u *AppUpdate) SetNillableCreatedAt(v *time.Time) *AppUpdate {
	if v != nil {
		_u.SetCreatedAt(*v)
	}
	return _u
}

// SetUpdatedAt sets the "updated_at" field.
func (_u *AppUpdate) SetUpdatedAt(v time.Time) *AppUpdate {
	_u.mutation.SetUpdatedAt(v)
	return _u
}

// Mutation returns the AppMutation object of the builder.
func (_u *AppUpdate) Mutation() *AppMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *AppUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AppUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *AppUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AppUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AppUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedAt(); !ok {
		v := app.UpdateDefaultUpdatedAt()
		_u.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *AppUpdate) check() error {
	if v, ok := _u.mutation.Name(); ok {
		if err := app.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "App.name": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Description(); ok {
		if err := app.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "App.description": %w`, err)}
		}
	}
	if v, ok := _u.mutation.APIKey(); ok {
		if err := app.APIKeyValidator(v); err != nil {
			return &ValidationError{Name: "api_key", err: fmt.Errorf(`ent: validator failed for field "App.api_key": %w`, err)}
		}
	}
	if v, ok := _u.mutation.NotifyURL(); ok {
		if err := app.NotifyURLValidator(v); err != nil {
			return &ValidationError{Name: "notify_url", err: fmt.Errorf(`ent: validator failed for field "App.notify_url": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := app.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "App.status": %w`, err)}
		}
	}
	return nil
}

func (_u *AppUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(app.Table, app.Columns, sqlgraph.NewFieldSpec(app.FieldID, field.TypeInt))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(app.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Description(); ok {
		_spec.SetField(app.FieldDescription, field.TypeString, value)
	}
	if value, ok := _u.mutation.APIKey(); ok {
		_spec.SetField(app.FieldAPIKey, field.TypeString, value)
	}
	if value, ok := _u.mutation.NotifyURL(); ok {
		_spec.SetField(app.FieldNotifyURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(app.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CreatedAt(); ok {
		_spec.SetField(app.FieldCreatedAt, field.TypeTime, value)
	}
	if value, ok := _u.mutation.UpdatedAt(); ok {
		_spec.SetField(app.FieldUpdatedAt, field.TypeTime, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{app.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// AppUpdateOne is the builder for updating a single App entity.
type AppUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AppMutation
}

// SetName sets the "name" field.
func (_u *AppUpdateOne) SetName(v string) *AppUpdateOne {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *AppUpdateOne) SetNillableName(v *string) *AppUpdateOne {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetDescription sets the "description" field.
func (_u *AppUpdateOne) SetDescription(v string) *AppUpdateOne {
	_u.mutation.SetDescription(v)
	return _u
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (_u *AppUpdateOne) SetNillableDescription(v *string) *AppUpdateOne {
	if v != nil {
		_u.SetDescription(*v)
	}
	return _u
}

// SetAPIKey sets the "api_key" field.
func (_u *AppUpdateOne) SetAPIKey(v string) *AppUpdateOne {
	_u.mutation.SetAPIKey(v)
	return _u
}

// SetNillableAPIKey sets the "api_key" field if the given value is not nil.
func (_u *AppUpdateOne) SetNillableAPIKey(v *string) *AppUpdateOne {
	if v != nil {
		_u.SetAPIKey(*v)
	}
	return _u
}

// SetNotifyURL sets the "notify_url" field.
func (_u *AppUpdateOne) SetNotifyURL(v string) *AppUpdateOne {
	_u.mutation.SetNotifyURL(v)
	return _u
}

// SetNillableNotifyURL sets the "notify_url" field if the given value is not nil.
func (_u *AppUpdateOne) SetNillableNotifyURL(v *string) *AppUpdateOne {
	if v != nil {
		_u.SetNotifyURL(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *AppUpdateOne) SetStatus(v app.Status) *AppUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *AppUpdateOne) SetNillableStatus(v *app.Status) *AppUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetCreatedAt sets the "created_at" field.
func (_u *AppUpdateOne) SetCreatedAt(v time.Time) *AppUpdateOne {
	_u.mutation.SetCreatedAt(v)
	return _u
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (_u *AppUpdateOne) SetNillableCreatedAt(v *time.Time) *AppUpdateOne {
	if v != nil {
		_u.SetCreatedAt(*v)
	}
	return _u
}

// SetUpdatedAt sets the "updated_at" field.
func (_u *AppUpdateOne) SetUpdatedAt(v time.Time) *AppUpdateOne {
	_u.mutation.SetUpdatedAt(v)
	return _u
}

// Mutation returns the AppMutation object of the builder.
func (_u *AppUpdateOne) Mutation() *AppMutation {
	return _u.mutation
}

// Where appends a list predicates to the AppUpdate builder.
func (_u *AppUpdateOne) Where(ps ...predicate.App) *AppUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *AppUpdateOne) Select(field string, fields ...string) *AppUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated App entity.
func (_u *AppUpdateOne) Save(ctx context.Context) (*App, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *AppUpdateOne) SaveX(ctx context.Context) *App {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *AppUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *AppUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *AppUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedAt(); !ok {
		v := app.UpdateDefaultUpdatedAt()
		_u.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *AppUpdateOne) check() error {
	if v, ok := _u.mutation.Name(); ok {
		if err := app.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "App.name": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Description(); ok {
		if err := app.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "App.description": %w`, err)}
		}
	}
	if v, ok := _u.mutation.APIKey(); ok {
		if err := app.APIKeyValidator(v); err != nil {
			return &ValidationError{Name: "api_key", err: fmt.Errorf(`ent: validator failed for field "App.api_key": %w`, err)}
		}
	}
	if v, ok := _u.mutation.NotifyURL(); ok {
		if err := app.NotifyURLValidator(v); err != nil {
			return &ValidationError{Name: "notify_url", err: fmt.Errorf(`ent: validator failed for field "App.notify_url": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := app.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "App.status": %w`, err)}
		}
	}
	return nil
}

func (_u *AppUpdateOne) sqlSave(ctx context.Context) (_node *App, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(app.Table, app.Columns, sqlgraph.NewFieldSpec(app.FieldID, field.TypeInt))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "App.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, app.FieldID)
		for _, f := range fields {
			if !app.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != app.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(app.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Description(); ok {
		_spec.SetField(app.FieldDescription, field.TypeString, value)
	}
	if value, ok := _u.mutation.APIKey(); ok {
		_spec.SetField(app.FieldAPIKey, field.TypeString, value)
	}
	if value, ok := _u.mutation.NotifyURL(); ok {
		_spec.SetField(app.FieldNotifyURL, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(app.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CreatedAt(); ok {
		_spec.SetField(app.FieldCreatedAt, field.TypeTime, value)
	}
	if value, ok := _u.mutation.UpdatedAt(); ok {
		_spec.SetField(app.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &App{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{app.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
