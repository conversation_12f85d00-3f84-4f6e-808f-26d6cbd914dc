// Code generated by ent, DO NOT EDIT.

package ent

import (
	"time"

	"git.yingrtech.com/yingrtech/clerk/ent/app"
	"git.yingrtech.com/yingrtech/clerk/ent/schema"
	"git.yingrtech.com/yingrtech/clerk/ent/user"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	appFields := schema.App{}.Fields()
	_ = appFields
	// appDescName is the schema descriptor for name field.
	appDescName := appFields[0].Descriptor()
	// app.NameValidator is a validator for the "name" field. It is called by the builders before save.
	app.NameValidator = appDescName.Validators[0].(func(string) error)
	// appDescDescription is the schema descriptor for description field.
	appDescDescription := appFields[1].Descriptor()
	// app.DescriptionValidator is a validator for the "description" field. It is called by the builders before save.
	app.DescriptionValidator = appDescDescription.Validators[0].(func(string) error)
	// appDescAPIKey is the schema descriptor for api_key field.
	appDescAPIKey := appFields[2].Descriptor()
	// app.APIKeyValidator is a validator for the "api_key" field. It is called by the builders before save.
	app.APIKeyValidator = appDescAPIKey.Validators[0].(func(string) error)
	// appDescNotifyURL is the schema descriptor for notify_url field.
	appDescNotifyURL := appFields[3].Descriptor()
	// app.NotifyURLValidator is a validator for the "notify_url" field. It is called by the builders before save.
	app.NotifyURLValidator = appDescNotifyURL.Validators[0].(func(string) error)
	// appDescCreatedAt is the schema descriptor for created_at field.
	appDescCreatedAt := appFields[5].Descriptor()
	// app.DefaultCreatedAt holds the default value on creation for the created_at field.
	app.DefaultCreatedAt = appDescCreatedAt.Default.(time.Time)
	// appDescUpdatedAt is the schema descriptor for updated_at field.
	appDescUpdatedAt := appFields[6].Descriptor()
	// app.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	app.DefaultUpdatedAt = appDescUpdatedAt.Default.(time.Time)
	// app.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	app.UpdateDefaultUpdatedAt = appDescUpdatedAt.UpdateDefault.(func() time.Time)
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescUsername is the schema descriptor for username field.
	userDescUsername := userFields[0].Descriptor()
	// user.UsernameValidator is a validator for the "username" field. It is called by the builders before save.
	user.UsernameValidator = userDescUsername.Validators[0].(func(string) error)
	// userDescEmail is the schema descriptor for email field.
	userDescEmail := userFields[1].Descriptor()
	// user.EmailValidator is a validator for the "email" field. It is called by the builders before save.
	user.EmailValidator = userDescEmail.Validators[0].(func(string) error)
	// userDescPassword is the schema descriptor for password field.
	userDescPassword := userFields[2].Descriptor()
	// user.PasswordValidator is a validator for the "password" field. It is called by the builders before save.
	user.PasswordValidator = userDescPassword.Validators[0].(func(string) error)
	// userDescName is the schema descriptor for name field.
	userDescName := userFields[3].Descriptor()
	// user.NameValidator is a validator for the "name" field. It is called by the builders before save.
	user.NameValidator = userDescName.Validators[0].(func(string) error)
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userFields[5].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userFields[6].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
}
