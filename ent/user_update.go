// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"git.yingrtech.com/yingrtech/clerk/ent/predicate"
	"git.yingrtech.com/yingrtech/clerk/ent/user"
)

// UserUpdate is the builder for updating User entities.
type UserUpdate struct {
	config
	hooks    []Hook
	mutation *UserMutation
}

// Where appends a list predicates to the UserUpdate builder.
func (_u *UserUpdate) Where(ps ...predicate.User) *UserUpdate {
	_u.mutation.Where(ps...)
	return _u
}

// SetUsername sets the "username" field.
func (_u *UserUpdate) SetUsername(v string) *UserUpdate {
	_u.mutation.SetUsername(v)
	return _u
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (_u *UserUpdate) SetNillableUsername(v *string) *UserUpdate {
	if v != nil {
		_u.SetUsername(*v)
	}
	return _u
}

// SetEmail sets the "email" field.
func (_u *UserUpdate) SetEmail(v string) *UserUpdate {
	_u.mutation.SetEmail(v)
	return _u
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (_u *UserUpdate) SetNillableEmail(v *string) *UserUpdate {
	if v != nil {
		_u.SetEmail(*v)
	}
	return _u
}

// SetPassword sets the "password" field.
func (_u *UserUpdate) SetPassword(v string) *UserUpdate {
	_u.mutation.SetPassword(v)
	return _u
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (_u *UserUpdate) SetNillablePassword(v *string) *UserUpdate {
	if v != nil {
		_u.SetPassword(*v)
	}
	return _u
}

// SetName sets the "name" field.
func (_u *UserUpdate) SetName(v string) *UserUpdate {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *UserUpdate) SetNillableName(v *string) *UserUpdate {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *UserUpdate) SetStatus(v user.Status) *UserUpdate {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *UserUpdate) SetNillableStatus(v *user.Status) *UserUpdate {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetCreatedAt sets the "created_at" field.
func (_u *UserUpdate) SetCreatedAt(v time.Time) *UserUpdate {
	_u.mutation.SetCreatedAt(v)
	return _u
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (_u *UserUpdate) SetNillableCreatedAt(v *time.Time) *UserUpdate {
	if v != nil {
		_u.SetCreatedAt(*v)
	}
	return _u
}

// SetUpdatedAt sets the "updated_at" field.
func (_u *UserUpdate) SetUpdatedAt(v time.Time) *UserUpdate {
	_u.mutation.SetUpdatedAt(v)
	return _u
}

// Mutation returns the UserMutation object of the builder.
func (_u *UserUpdate) Mutation() *UserMutation {
	return _u.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (_u *UserUpdate) Save(ctx context.Context) (int, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *UserUpdate) SaveX(ctx context.Context) int {
	affected, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (_u *UserUpdate) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *UserUpdate) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *UserUpdate) defaults() {
	if _, ok := _u.mutation.UpdatedAt(); !ok {
		v := user.UpdateDefaultUpdatedAt()
		_u.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *UserUpdate) check() error {
	if v, ok := _u.mutation.Username(); ok {
		if err := user.UsernameValidator(v); err != nil {
			return &ValidationError{Name: "username", err: fmt.Errorf(`ent: validator failed for field "User.username": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Password(); ok {
		if err := user.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`ent: validator failed for field "User.password": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Name(); ok {
		if err := user.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "User.name": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := user.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "User.status": %w`, err)}
		}
	}
	return nil
}

func (_u *UserUpdate) sqlSave(ctx context.Context) (_node int, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
	}
	if value, ok := _u.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if value, ok := _u.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CreatedAt(); ok {
		_spec.SetField(user.FieldCreatedAt, field.TypeTime, value)
	}
	if value, ok := _u.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if _node, err = sqlgraph.UpdateNodes(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	_u.mutation.done = true
	return _node, nil
}

// UserUpdateOne is the builder for updating a single User entity.
type UserUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserMutation
}

// SetUsername sets the "username" field.
func (_u *UserUpdateOne) SetUsername(v string) *UserUpdateOne {
	_u.mutation.SetUsername(v)
	return _u
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (_u *UserUpdateOne) SetNillableUsername(v *string) *UserUpdateOne {
	if v != nil {
		_u.SetUsername(*v)
	}
	return _u
}

// SetEmail sets the "email" field.
func (_u *UserUpdateOne) SetEmail(v string) *UserUpdateOne {
	_u.mutation.SetEmail(v)
	return _u
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (_u *UserUpdateOne) SetNillableEmail(v *string) *UserUpdateOne {
	if v != nil {
		_u.SetEmail(*v)
	}
	return _u
}

// SetPassword sets the "password" field.
func (_u *UserUpdateOne) SetPassword(v string) *UserUpdateOne {
	_u.mutation.SetPassword(v)
	return _u
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (_u *UserUpdateOne) SetNillablePassword(v *string) *UserUpdateOne {
	if v != nil {
		_u.SetPassword(*v)
	}
	return _u
}

// SetName sets the "name" field.
func (_u *UserUpdateOne) SetName(v string) *UserUpdateOne {
	_u.mutation.SetName(v)
	return _u
}

// SetNillableName sets the "name" field if the given value is not nil.
func (_u *UserUpdateOne) SetNillableName(v *string) *UserUpdateOne {
	if v != nil {
		_u.SetName(*v)
	}
	return _u
}

// SetStatus sets the "status" field.
func (_u *UserUpdateOne) SetStatus(v user.Status) *UserUpdateOne {
	_u.mutation.SetStatus(v)
	return _u
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_u *UserUpdateOne) SetNillableStatus(v *user.Status) *UserUpdateOne {
	if v != nil {
		_u.SetStatus(*v)
	}
	return _u
}

// SetCreatedAt sets the "created_at" field.
func (_u *UserUpdateOne) SetCreatedAt(v time.Time) *UserUpdateOne {
	_u.mutation.SetCreatedAt(v)
	return _u
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (_u *UserUpdateOne) SetNillableCreatedAt(v *time.Time) *UserUpdateOne {
	if v != nil {
		_u.SetCreatedAt(*v)
	}
	return _u
}

// SetUpdatedAt sets the "updated_at" field.
func (_u *UserUpdateOne) SetUpdatedAt(v time.Time) *UserUpdateOne {
	_u.mutation.SetUpdatedAt(v)
	return _u
}

// Mutation returns the UserMutation object of the builder.
func (_u *UserUpdateOne) Mutation() *UserMutation {
	return _u.mutation
}

// Where appends a list predicates to the UserUpdate builder.
func (_u *UserUpdateOne) Where(ps ...predicate.User) *UserUpdateOne {
	_u.mutation.Where(ps...)
	return _u
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (_u *UserUpdateOne) Select(field string, fields ...string) *UserUpdateOne {
	_u.fields = append([]string{field}, fields...)
	return _u
}

// Save executes the query and returns the updated User entity.
func (_u *UserUpdateOne) Save(ctx context.Context) (*User, error) {
	_u.defaults()
	return withHooks(ctx, _u.sqlSave, _u.mutation, _u.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (_u *UserUpdateOne) SaveX(ctx context.Context) *User {
	node, err := _u.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (_u *UserUpdateOne) Exec(ctx context.Context) error {
	_, err := _u.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_u *UserUpdateOne) ExecX(ctx context.Context) {
	if err := _u.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_u *UserUpdateOne) defaults() {
	if _, ok := _u.mutation.UpdatedAt(); !ok {
		v := user.UpdateDefaultUpdatedAt()
		_u.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_u *UserUpdateOne) check() error {
	if v, ok := _u.mutation.Username(); ok {
		if err := user.UsernameValidator(v); err != nil {
			return &ValidationError{Name: "username", err: fmt.Errorf(`ent: validator failed for field "User.username": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Email(); ok {
		if err := user.EmailValidator(v); err != nil {
			return &ValidationError{Name: "email", err: fmt.Errorf(`ent: validator failed for field "User.email": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Password(); ok {
		if err := user.PasswordValidator(v); err != nil {
			return &ValidationError{Name: "password", err: fmt.Errorf(`ent: validator failed for field "User.password": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Name(); ok {
		if err := user.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "User.name": %w`, err)}
		}
	}
	if v, ok := _u.mutation.Status(); ok {
		if err := user.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "User.status": %w`, err)}
		}
	}
	return nil
}

func (_u *UserUpdateOne) sqlSave(ctx context.Context) (_node *User, err error) {
	if err := _u.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt))
	id, ok := _u.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "User.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := _u.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for _, f := range fields {
			if !user.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := _u.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := _u.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
	}
	if value, ok := _u.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if value, ok := _u.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := _u.mutation.Name(); ok {
		_spec.SetField(user.FieldName, field.TypeString, value)
	}
	if value, ok := _u.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeEnum, value)
	}
	if value, ok := _u.mutation.CreatedAt(); ok {
		_spec.SetField(user.FieldCreatedAt, field.TypeTime, value)
	}
	if value, ok := _u.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	_node = &User{config: _u.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, _u.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	_u.mutation.done = true
	return _node, nil
}
