// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"git.yingrtech.com/yingrtech/clerk/ent/app"
)

// AppCreate is the builder for creating a App entity.
type AppCreate struct {
	config
	mutation *AppMutation
	hooks    []Hook
}

// SetName sets the "name" field.
func (_c *AppCreate) SetName(v string) *AppCreate {
	_c.mutation.SetName(v)
	return _c
}

// SetDescription sets the "description" field.
func (_c *AppCreate) SetDescription(v string) *AppCreate {
	_c.mutation.SetDescription(v)
	return _c
}

// SetAPIKey sets the "api_key" field.
func (_c *AppCreate) SetAPIKey(v string) *AppCreate {
	_c.mutation.SetAPIKey(v)
	return _c
}

// SetNotifyURL sets the "notify_url" field.
func (_c *AppCreate) SetNotifyURL(v string) *AppCreate {
	_c.mutation.SetNotifyURL(v)
	return _c
}

// SetStatus sets the "status" field.
func (_c *AppCreate) SetStatus(v app.Status) *AppCreate {
	_c.mutation.SetStatus(v)
	return _c
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (_c *AppCreate) SetNillableStatus(v *app.Status) *AppCreate {
	if v != nil {
		_c.SetStatus(*v)
	}
	return _c
}

// SetCreatedAt sets the "created_at" field.
func (_c *AppCreate) SetCreatedAt(v time.Time) *AppCreate {
	_c.mutation.SetCreatedAt(v)
	return _c
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (_c *AppCreate) SetNillableCreatedAt(v *time.Time) *AppCreate {
	if v != nil {
		_c.SetCreatedAt(*v)
	}
	return _c
}

// SetUpdatedAt sets the "updated_at" field.
func (_c *AppCreate) SetUpdatedAt(v time.Time) *AppCreate {
	_c.mutation.SetUpdatedAt(v)
	return _c
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (_c *AppCreate) SetNillableUpdatedAt(v *time.Time) *AppCreate {
	if v != nil {
		_c.SetUpdatedAt(*v)
	}
	return _c
}

// Mutation returns the AppMutation object of the builder.
func (_c *AppCreate) Mutation() *AppMutation {
	return _c.mutation
}

// Save creates the App in the database.
func (_c *AppCreate) Save(ctx context.Context) (*App, error) {
	_c.defaults()
	return withHooks(ctx, _c.sqlSave, _c.mutation, _c.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (_c *AppCreate) SaveX(ctx context.Context) *App {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AppCreate) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AppCreate) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (_c *AppCreate) defaults() {
	if _, ok := _c.mutation.Status(); !ok {
		v := app.DefaultStatus
		_c.mutation.SetStatus(v)
	}
	if _, ok := _c.mutation.CreatedAt(); !ok {
		v := app.DefaultCreatedAt
		_c.mutation.SetCreatedAt(v)
	}
	if _, ok := _c.mutation.UpdatedAt(); !ok {
		v := app.DefaultUpdatedAt
		_c.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (_c *AppCreate) check() error {
	if _, ok := _c.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "App.name"`)}
	}
	if v, ok := _c.mutation.Name(); ok {
		if err := app.NameValidator(v); err != nil {
			return &ValidationError{Name: "name", err: fmt.Errorf(`ent: validator failed for field "App.name": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "App.description"`)}
	}
	if v, ok := _c.mutation.Description(); ok {
		if err := app.DescriptionValidator(v); err != nil {
			return &ValidationError{Name: "description", err: fmt.Errorf(`ent: validator failed for field "App.description": %w`, err)}
		}
	}
	if _, ok := _c.mutation.APIKey(); !ok {
		return &ValidationError{Name: "api_key", err: errors.New(`ent: missing required field "App.api_key"`)}
	}
	if v, ok := _c.mutation.APIKey(); ok {
		if err := app.APIKeyValidator(v); err != nil {
			return &ValidationError{Name: "api_key", err: fmt.Errorf(`ent: validator failed for field "App.api_key": %w`, err)}
		}
	}
	if _, ok := _c.mutation.NotifyURL(); !ok {
		return &ValidationError{Name: "notify_url", err: errors.New(`ent: missing required field "App.notify_url"`)}
	}
	if v, ok := _c.mutation.NotifyURL(); ok {
		if err := app.NotifyURLValidator(v); err != nil {
			return &ValidationError{Name: "notify_url", err: fmt.Errorf(`ent: validator failed for field "App.notify_url": %w`, err)}
		}
	}
	if _, ok := _c.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "App.status"`)}
	}
	if v, ok := _c.mutation.Status(); ok {
		if err := app.StatusValidator(v); err != nil {
			return &ValidationError{Name: "status", err: fmt.Errorf(`ent: validator failed for field "App.status": %w`, err)}
		}
	}
	if _, ok := _c.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "App.created_at"`)}
	}
	if _, ok := _c.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "App.updated_at"`)}
	}
	return nil
}

func (_c *AppCreate) sqlSave(ctx context.Context) (*App, error) {
	if err := _c.check(); err != nil {
		return nil, err
	}
	_node, _spec := _c.createSpec()
	if err := sqlgraph.CreateNode(ctx, _c.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	_c.mutation.id = &_node.ID
	_c.mutation.done = true
	return _node, nil
}

func (_c *AppCreate) createSpec() (*App, *sqlgraph.CreateSpec) {
	var (
		_node = &App{config: _c.config}
		_spec = sqlgraph.NewCreateSpec(app.Table, sqlgraph.NewFieldSpec(app.FieldID, field.TypeInt))
	)
	if value, ok := _c.mutation.Name(); ok {
		_spec.SetField(app.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := _c.mutation.Description(); ok {
		_spec.SetField(app.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := _c.mutation.APIKey(); ok {
		_spec.SetField(app.FieldAPIKey, field.TypeString, value)
		_node.APIKey = value
	}
	if value, ok := _c.mutation.NotifyURL(); ok {
		_spec.SetField(app.FieldNotifyURL, field.TypeString, value)
		_node.NotifyURL = value
	}
	if value, ok := _c.mutation.Status(); ok {
		_spec.SetField(app.FieldStatus, field.TypeEnum, value)
		_node.Status = value
	}
	if value, ok := _c.mutation.CreatedAt(); ok {
		_spec.SetField(app.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := _c.mutation.UpdatedAt(); ok {
		_spec.SetField(app.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	return _node, _spec
}

// AppCreateBulk is the builder for creating many App entities in bulk.
type AppCreateBulk struct {
	config
	err      error
	builders []*AppCreate
}

// Save creates the App entities in the database.
func (_c *AppCreateBulk) Save(ctx context.Context) ([]*App, error) {
	if _c.err != nil {
		return nil, _c.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(_c.builders))
	nodes := make([]*App, len(_c.builders))
	mutators := make([]Mutator, len(_c.builders))
	for i := range _c.builders {
		func(i int, root context.Context) {
			builder := _c.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AppMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, _c.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, _c.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, _c.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (_c *AppCreateBulk) SaveX(ctx context.Context) []*App {
	v, err := _c.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (_c *AppCreateBulk) Exec(ctx context.Context) error {
	_, err := _c.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (_c *AppCreateBulk) ExecX(ctx context.Context) {
	if err := _c.Exec(ctx); err != nil {
		panic(err)
	}
}
