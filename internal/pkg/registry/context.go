package registry

import (
	"git.yingrtech.com/yingrtech/clerk/config"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/cache"
	"github.com/redis/rueidis"
)

type AppContext struct {
	Config      *config.Config
	RedisClient rueidis.Client
}

func NewContext(path string) (*AppContext, error) {
	cfg, err := config.New(path)
	if err != nil {
		return nil, err
	}

	redisClient, err := cache.NewClient(cfg.Redis)
	if err != nil {
		return nil, err
	}

	return &AppContext{
		Config:      cfg,
		RedisClient: redisClient,
	}, nil
}
