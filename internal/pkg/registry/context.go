package container

import (
	"git.yingrtech.com/yingrtech/clerk/config"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/cache"
	"github.com/redis/rueidis"
)

// Container 应用程序依赖注入容器，整合所有全局服务
type Container struct {
	Config      *config.Config
	RedisClient rueidis.Client
}

// New 创建新的应用程序容器
func New(configPath string) (*Container, error) {
	cfg, err := config.New(configPath)
	if err != nil {
		return nil, err
	}

	redisClient, err := cache.NewClient(cfg.Redis)
	if err != nil {
		return nil, err
	}

	return &Container{
		Config:      cfg,
		RedisClient: redisClient,
	}, nil
}
