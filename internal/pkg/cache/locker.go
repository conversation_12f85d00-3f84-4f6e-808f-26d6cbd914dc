package cache

import (
	"context"
	"errors"
	"fmt"
	"math/rand/v2"
	"time"

	"github.com/redis/rueidis"
)

const acquireLuaScript = `
if redis.call("setnx", KEYS[1], ARGV[1]) == 1 then
    redis.call("pexpire", KEYS[1], ARGV[2])
    return 1
else
    return 0
end
`

const releaseLuaScript = `
if redis.call("get", KEYS[1]) == ARGV[1] then
    return redis.call("del", KEYS[1])
else
    return 0
end
`

var (
	acquireScript = rueidis.NewLuaScript(acquireLuaScript)
	releaseScript = rueidis.NewLuaScript(releaseLuaScript)

	ErrLockNotAcquired = errors.New("lock not acquired")
	ErrUnlockFailed    = errors.New("unlock failed")
)

type Locker struct {
	owner  string
	client rueidis.Client
}

func NewLocker(client rueidis.Client, owner string) *Locker {
	if owner == "" {
		const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
		b := make([]byte, 16)
		for i := range b {
			b[i] = charset[rand.IntN(len(charset))]
		}
		owner = string(b)
	}
	return &Locker{client: client, owner: owner}
}

func (l *Locker) Lock(ctx context.Context, key string, ttl time.Duration) (string, error) {
	result, err := acquireScript.Exec(
		ctx,
		l.client,
		[]string{key},
		[]string{l.owner, fmt.Sprintf("%d", int64(ttl/time.Millisecond))},
	).AsInt64()
	if err != nil {
		return "", err
	}
	if result == 0 {
		return "", ErrLockNotAcquired
	}
	return l.owner, nil
}

func (l *Locker) Block(ctx context.Context, key string, ttl time.Duration, wait time.Duration) error {
	deadline := time.Now().Add(wait)
	for {
		if _, err := l.Lock(ctx, key, ttl); err == nil {
			return nil
		} else if err != ErrLockNotAcquired {
			return err
		}

		if time.Now().After(deadline) {
			return ErrLockNotAcquired
		}
		select {
		case <-time.After(50 * time.Millisecond):
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

func (l *Locker) Unlock(ctx context.Context, key string) error {
	result, err := releaseScript.Exec(
		ctx,
		l.client,
		[]string{key},
		[]string{l.owner},
	).AsInt64()
	if err != nil {
		return err
	}
	if result == 0 {
		return ErrUnlockFailed
	}
	return nil
}
