package cache

import (
	"fmt"

	"git.yingrtech.com/yingrtech/clerk/config"
	"github.com/redis/rueidis"
)

func NewClient(redisConfig config.Redis) (rueidis.Client, error) {
	return rueidis.NewClient(rueidis.ClientOption{
		InitAddress: []string{
			fmt.Sprintf("%s:%d", redisConfig.Host, redisConfig.Port),
		},
		Username: redisConfig.Username,
		Password: redisConfig.Password,
		SelectDB: redisConfig.Database,
	})
}
