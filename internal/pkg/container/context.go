package container

import (
	"log/slog"

	"git.yingrtech.com/yingrtech/clerk/config"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/cache"
	"github.com/redis/rueidis"
)

// Container 应用程序依赖注入容器，整合所有全局服务
// 用于在整个应用程序中传递共享的服务实例，避免使用全局变量
type Container struct {
	Config      *config.Config
	RedisClient rueidis.Client
}

// New 创建新的应用程序容器
// configPath: 配置文件路径
func New(configPath string) (*Container, error) {
	cfg, err := config.New(configPath)
	if err != nil {
		slog.Error("Failed to load config", "path", configPath, "error", err)
		return nil, err
	}

	redisClient, err := cache.NewClient(cfg.Redis)
	if err != nil {
		slog.Error("Failed to create Redis client", "error", err)
		return nil, err
	}

	slog.Info("Container initialized successfully")
	return &Container{
		Config:      cfg,
		RedisClient: redisClient,
	}, nil
}

// Close 关闭容器中的所有资源
func (c *Container) Close() error {
	if c.RedisClient != nil {
		c.RedisClient.Close()
		slog.Info("Redis client closed")
	}
	return nil
}

// IsProduction 检查是否为生产环境
func (c *Container) IsProduction() bool {
	return c.Config.IsProduction()
}
