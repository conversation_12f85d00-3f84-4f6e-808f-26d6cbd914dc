package router

import (
	"net/http"

	"git.yingrtech.com/yingrtech/clerk/internal/app/middleware"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/container"
	echojwt "github.com/labstack/echo-jwt/v4"
	"github.com/labstack/echo/v4"
)

func Register(e *echo.Echo, ctx *container.Container) {
	e.GET("/", func(c echo.Context) error {
		return c.String(http.StatusOK, "Hello, World!")
	})

	{
		r := e.Group("/api/v1", echojwt.JWT([]byte(ctx.Config.JWT.Secret)), middleware.WithUser)
		r.GET("/users", func(c echo.Context) error {
			return c.String(http.StatusOK, "Hello, World!")
		})
	}
}
