package app

import (
	"context"
	"log/slog"

	"git.yingrtech.com/yingrtech/clerk/internal/pkg/container"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	slogecho "github.com/samber/slog-echo"
)

type App struct {
	e   *echo.Echo
	Ctx *container.Container
}

func New(appCtx *container.Container) *App {
	e := echo.New()
	return &App{e: e, Ctx: appCtx}
}

func (a *App) registerMiddlewares() {
	a.e.Use(slogecho.New(slog.Default()))
	a.e.Use(middleware.Recover())
	a.e.Use(middleware.CORS())
	a.e.Use(middleware.RequestID())

}

func (a *App) Start() error {
	a.registerMiddlewares()

	return a.e.Start(a.Ctx.Config.App.ListenAddress)
}

func (a *App) Shutdown(ctx context.Context) error {
	return a.e.Shutdown(ctx)
}
