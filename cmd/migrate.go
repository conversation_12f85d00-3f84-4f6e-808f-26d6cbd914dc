package cmd

import (
	"context"
	"fmt"
	"log/slog"
	"os"

	atlas "ariga.io/atlas/sql/migrate"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql/schema"
	"git.yingrtech.com/yingrtech/clerk/ent/migrate"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/registry"
	_ "github.com/go-sql-driver/mysql"
	"github.com/urfave/cli/v3"
)

const migrationsDir = "ent/migrate/migrations"

func migrateCmd(appCtx *registry.AppContext) *cli.Command {
	return &cli.Command{
		Name:  "migrate",
		Usage: "migrate the database",
		Arguments: []cli.Argument{
			&cli.StringArg{Name: "name", UsageText: "name of the migration"},
		},
		Action: func(ctx context.Context, c *cli.Command) error {
			if err := makeMigrationsDirectory(); err != nil {
				return err
			}

			dir, err := atlas.NewLocalDir(migrationsDir)
			if err != nil {
				slog.Error("Failed creating atlas migration directory", "error", err)
				return err
			}

			opts := []schema.MigrateOption{
				schema.WithDir(dir),                         // provide migration directory
				schema.WithMigrationMode(schema.ModeReplay), // provide migration mode
				schema.WithDialect(dialect.MySQL),           // Ent dialect to use
				schema.WithFormatter(atlas.DefaultFormatter),
			}
			if err := migrate.NamedDiff(ctx, buildDatabaseDsn(appCtx), c.String("name"), opts...); err != nil {
				slog.Error("failed generating migration file", "error", err)
				return err
			}
			return nil
		},
	}
}

func buildDatabaseDsn(appCtx *registry.AppContext) string {
	return fmt.Sprintf("mysql://%s:%s@%s:%d/%s",
		appCtx.Config.Database.Username,
		appCtx.Config.Database.Password,
		appCtx.Config.Database.Host,
		appCtx.Config.Database.Port,
		appCtx.Config.Database.Database)
}

func makeMigrationsDirectory() error {
	if _, err := os.Stat(migrationsDir); os.IsNotExist(err) {
		return os.MkdirAll(migrationsDir, 0755)
	}
	return nil
}
