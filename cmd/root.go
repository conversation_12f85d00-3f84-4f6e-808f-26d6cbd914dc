package cmd

import (
	"context"
	"os"

	"git.yingrtech.com/yingrtech/clerk/internal/pkg/container"
	"github.com/urfave/cli/v3"
)

var root = &cli.Command{
	Name:    "clerk",
	Usage:   "自动录单",
	Version: "1.0.0",
}

func Run(appCtx *container.Container, version string) error {
	root.Version = version
	root.Commands = []*cli.Command{
		serveCmd(appCtx),
		migrateCmd(appCtx),
	}

	return root.Run(context.Background(), os.Args)
}
