package cmd

import (
	"context"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"time"

	"git.yingrtech.com/yingrtech/clerk/internal/app"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/container"
	"github.com/urfave/cli/v3"
)

func serveCmd(appCtx *container.Container) *cli.Command {
	return &cli.Command{
		Name:  "serve",
		Usage: "serve the application",
		Action: func(ctx context.Context, c *cli.Command) error {
			app := app.New(appCtx)
			ctx, stop := signal.NotifyContext(ctx, os.Interrupt)
			defer stop()

			go func() {
				if err := app.Start(); err != nil && err != http.ErrServerClosed {
					slog.Error("Unable to start echo", "error", err)
					panic(err)
				}
			}()

			<-ctx.Done()

			ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
			defer cancel()
			if err := app.Shutdown(ctx); err != nil {
				slog.Error("Unable to shutdown echo", "error", err)
				return err
			}
			return nil
		},
	}
}
