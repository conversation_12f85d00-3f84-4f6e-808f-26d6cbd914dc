# clerk

全新的基于 Go 语言的自动录单管理平台

## Migrations

### 创建新模型

```bash
go run -mod=mod entgo.io/ent/cmd/ent new ModelName
```

### 修改表结构

修改 `ent/schema` 后，请务必按以下步骤生成 migration 文件

```bash
DATABASE_URL=mysql://root:pass@localhost:3306/example
MIGRATION_NAME=create_users

# Generate ent schema
go generate ./ent

# Generate migration files
go run main.go migrate $MIGRATION_NAME

# Apply migrations
atlas migrate apply \
  --dir "file://ent/migrate/migrations" \
  --url $DATABASE_URL
```
