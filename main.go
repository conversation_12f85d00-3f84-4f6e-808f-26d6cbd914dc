package main

import (
	"errors"
	"flag"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"time"

	"git.yingrtech.com/yingrtech/clerk/cmd"
	"git.yingrtech.com/yingrtech/clerk/internal/pkg/registry"
)

var BuildVersion string

var (
	configPath string
)

func init() {
	wd, _ := os.Getwd()
	flag.StringVar(&configPath, "config", filepath.Join(wd, "config.toml"), "The path where the config file is located")
	flag.Parse()
}

func main() {
	ctx, err := registry.NewContext(configPath)
	if err != nil {
		slog.Error("Unable to initialize clerk", "error", err)
		os.Exit(1)
	}

	if err := setLogger(); err != nil {
		slog.Error("Unable to set logger", "error", err)
		os.Exit(1)
	}

	if !ctx.Config.IsProduction() {
		slog.SetLogLoggerLevel(slog.LevelDebug)
	}

	if err := cmd.Run(ctx, BuildVersion); err != nil {
		slog.Error("Unable to run clerk", "error", err)
		os.Exit(1)
	}
}

func setLogger() error {
	wd, err := os.Getwd()
	if err != nil {
		return err
	}
	if _, err := os.Stat(filepath.Join(wd, "logs")); err != nil {
		if errors.Is(err, os.ErrNotExist) {
			return os.MkdirAll(filepath.Join(wd, "logs"), 0755)
		}
		return err
	}
	fileName := filepath.Join(wd, "logs", fmt.Sprintf("clerk-%s.log", time.Now().Format("2006-01-02")))
	file, err := os.OpenFile(fileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return err
	}
	slog.SetDefault(slog.New(slog.NewJSONHandler(io.MultiWriter(file, os.Stdout), nil)))
	return nil
}
