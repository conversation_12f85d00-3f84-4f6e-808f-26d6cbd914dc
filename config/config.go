package config

import (
	"os"

	"github.com/pelletier/go-toml/v2"
)

type Environment string

const (
	EnvironmentDevelopment Environment = "development"
	EnvironmentProduction  Environment = "production"
)

type Config struct {
	App      App      `toml:"app"`
	Database Database `toml:"database"`
	Redis    Redis    `toml:"redis"`
	JWT      JWT      `toml:"jwt"`
}

type App struct {
	Environment   Environment `toml:"environment"`
	ListenAddress string      `toml:"listen_address"`
}

type JWT struct {
	Secret string `toml:"secret"`
}

type Database struct {
	Host     string `toml:"host"`
	Port     int    `toml:"port"`
	Username string `toml:"username"`
	Password string `toml:"password"`
	Database string `toml:"database"`
}

type Redis struct {
	Host     string `toml:"host"`
	Port     int    `toml:"port"`
	Username string `toml:"username"`
	Password string `toml:"password"`
	Database int    `toml:"database"`
}

func New(path string) (*Config, error) {
	f, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer f.Close()
	var c Config
	if err := toml.NewDecoder(f).Decode(&c); err != nil {
		return nil, err
	}
	return &c, nil
}

func (c *Config) IsProduction() bool {
	return c.App.Environment == EnvironmentProduction
}
